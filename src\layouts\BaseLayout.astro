---
// src/layouts/BaseLayout.astro
import Header from "../components/Header.astro";
import Footer from "../components/Footer.astro";
import IntroScreen from "../components/IntroScreen.astro";

// Import global styles
import "../styles/fonts.css";
import "../styles/global.css";

const { url } = Astro;
const isHome = url.pathname === "/";
---

<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <title>Astrog@mers</title>
    <script
      defer
      src="https://cloud.umami.is/script.js"
      data-website-id="44eefb18-9c71-4a3d-b396-1a963c4aa1cd"></script>
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  </head>

  <body>
    {isHome && <IntroScreen />}

    <Header />

    <main>
      <slot />
      <!-- Here will be the page-specific content -->
    </main>

    <Footer />
  </body>
</html>

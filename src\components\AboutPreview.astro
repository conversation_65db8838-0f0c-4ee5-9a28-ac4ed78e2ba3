---
import { Image } from "astro:assets";
import AboutPreviewGirl from "../assets/AboutPreviewGirl.webp";
import AboutPreviewDevice from "../assets/AboutPreviewDevice.webp";
import AstroWheel from "../assets/astro-wheel.webp";
import Icon from "./Icon.astro";
---

<section id="about-preview">
  <div class="about-grid">
    <div class="images-column">
      <Image
        src={AboutPreviewGirl}
        alt="femme voyant l’avenir"
        class="top-left"
        loading="lazy"
        decoding="async"
      />
      <Image
        src={AboutPreviewDevice}
        alt="périphériques"
        class="bottom-right"
        loading="lazy"
        decoding="async"
      />
      <Image
        src={AstroWheel}
        alt="Roue du zodiaque"
        class="wheel"
        loading="lazy"
        decoding="async"
      />
    </div>
    <div class="text-column">
      <p class="subtitle">A propos de nous</p>
      <h2>
        Savoir à l’avance si ta session va briller… ou partir en <em>flame</em>
      </h2>
      <p>
        Astrog@mers, c’est l’horoscope qui comprend tes ranked, tes clutchs
        ratés et ta malchance dans le matchmacking. Ici, chaque signe reçoit un
        boost quotidien (ou une claque cosmique) pour affronter le matchmaking
        avec sagesse, ou pas.
      </p>
      <ul class="features">
        <li>
          <Icon icon="rafter-left" /> 12 horoscopes par jour, un pour chaque signe
        </li><li>
          <Icon icon="rafter-left" /> Fonctionne même si Mercure est rétrograde
        </li><li>
          <Icon icon="rafter-left" /> Ton Elo cosmique recalculé chaque matin
        </li>
        <li>
          <Icon icon="rafter-left" /> Pas de tracking, pas de cookies, que des vibes
        </li>
      </ul>
    </div>
  </div>
</section>

<style>
  #about-preview {
    padding: 2rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .about-grid {
    max-width: 1200px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 1fr;
    gap: 3rem;
    align-items: center;
  }

  .text-column {
    grid-area: 1 / 2 / 2 / 4;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }

  .text-column h2 {
    font-family: "Orbitron", sans-serif;
    font-size: 2.2rem;
    margin: 0;
    line-height: 1.3;
  }

  .text-column p {
    font-size: 1rem;
    line-height: 1.6;
  }

  .images-column {
    grid-area: 1 / 1 / 2 / 2;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .top-left {
    border-top-left-radius: 50%;
    object-fit: cover;
    object-position: top center;
    background-repeat: no-repeat;
    height: 300px;
  }

  .bottom-right {
    border-bottom-right-radius: 50%;
    object-fit: cover;
    object-position: bottom center;
    background-repeat: no-repeat;
    height: 200px;
  }

  img.wheel {
    position: absolute;
    width: 120px;
    right: 5%;
    top: 50%;
    opacity: 1;
  }

  .features {
    padding-left: 0;
    list-style: none;
  }

  .features li {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: 0.95rem;
    color: var(--accent-regular);
  }

  @media (max-width: 1024px) {
    .about-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .text-column {
      grid-area: 2 / 1 / 3 / 2;
    }

    .images-column {
      grid-area: 1 / 1 / 2 / 2;
    }

    .top-left,
    .bottom-right {
      width: 100%;
      height: auto;
    }

    .wheel {
      width: 100px;
    }
  }
</style>

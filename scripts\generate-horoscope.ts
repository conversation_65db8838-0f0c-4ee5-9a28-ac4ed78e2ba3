import fs from "fs";
import path from "path";
import { GoogleGenAI } from "@google/genai";

// 1. Vérification de la clé API
if (!process.env.GEMINI_API_KEY) {
  console.error(
    "🚨 ERREUR: La variable d'environnement GEMINI_API_KEY n'est pas définie dans votre environnement."
  );
  process.exit(1);
}

// 2. ✅ CORRECTION CRITIQUE : Utilisation de la nouvelle classe GoogleGenAI
const genAI = new GoogleGenAI(process.env.GEMINI_API_KEY!);
const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

// 3. ✅ SÉCURISATION : Lecture du fichier dans un bloc try/catch
let promptContent = "";
const promptFilePath = "scripts/prompt.ts";

try {
  promptContent = fs.readFileSync(promptFilePath, "utf-8");
} catch (error) {
  console.error(
    `🚨 ERREUR: Le fichier prompt n'a pas pu être lu à: ${promptFilePath}`
  );
  console.error(error);
  process.exit(1);
}

// 4. Utilisation du contenu sécurisé
const prompt = `
  ${promptContent}

  Réponds UNIQUEMENT avec le contenu JSON valide, sans aucun texte, explication ou formatage \`\`\`json\`\`\` autour.
`;

async function getHoroscopes() {
  try {
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const jsonRaw = response.text();

    const cleanedJson = jsonRaw.replace(/^```json\s*/, "").replace(/```$/, "");

    const horoscopes = JSON.parse(cleanedJson);
    return horoscopes;
  } catch (error) {
    console.error("Erreur lors de la génération ou du parsing JSON:", error);
    process.exit(1);
  }
}

(async () => {
  const horoscopes = await getHoroscopes();
  if (horoscopes) {
    const output = `// Fichier auto-généré le ${new Date().toLocaleDateString()}}\nexport const horoscopes = ${JSON.stringify(
      horoscopes,
      null,
      2
    )};\n`;
    fs.writeFileSync(path.resolve("src/data/horoscopes.ts"), output, "utf-8");
    console.log("✅ Horoscope du jour généré !");
  }
})();

/* Global variables */
:root {
  /* Colors */
  --gray-0: #090b11;
  --gray-50: #141925;
  --gray-100: #283044;
  --gray-200: #3d4663;
  --gray-300: #505d84;
  --gray-400: #6474a2;
  --gray-500: #8490b5;
  --gray-600: #a3acc8;
  --gray-700: #c3cadb;
  --gray-800: #e3e6ee;
  --gray-900: #f3f4f7;
  --gray-999-basis: 0, 0%, 100%;
  --gray-999_40: hsla(var(--gray-999-basis), 0.4);
  --gray-999: #ffffff;

  --accent-light: #c561f6;
  --accent-regular: #7611a6;
  --accent-dark: #1c0056;
  --accent-overlay: hsla(280, 89%, 67%, 0.33);
  --accent-subtle-overlay: var(--accent-overlay);
  --accent-text-over: var(--gray-999);

  --link-color: var(--accent-regular);

  /* Gradients */
  --gradient-stop-1: var(--accent-light);
  --gradient-stop-2: var(--accent-regular);
  --gradient-stop-3: var(--accent-dark);
  --gradient-subtle: linear-gradient(
    150deg,
    var(--gray-900) 19%,
    var(--gray-999) 150%
  );
  --gradient-accent: linear-gradient(
    150deg,
    var(--gradient-stop-1),
    var(--gradient-stop-2),
    var(--gradient-stop-3)
  );
  --gradient-accent-orange: linear-gradient(
    150deg,
    #ca7879,
    var(--accent-regular),
    var(--accent-dark)
  );
  --gradient-stroke: linear-gradient(180deg, var(--gray-900), var(--gray-700));

  /* Shadows */
  --shadow-sm: 0px 6px 3px rgba(9, 11, 17, 0.01),
    0px 4px 2px rgba(9, 11, 17, 0.01), 0px 2px 2px rgba(9, 11, 17, 0.02),
    0px 0px 1px rgba(9, 11, 17, 0.03);
  --shadow-md: 0px 28px 11px rgba(9, 11, 17, 0.01),
    0px 16px 10px rgba(9, 11, 17, 0.03), 0px 7px 7px rgba(9, 11, 17, 0.05),
    0px 2px 4px rgba(9, 11, 17, 0.06);
  --shadow-lg: 0px 62px 25px rgba(9, 11, 17, 0.01),
    0px 35px 21px rgba(9, 11, 17, 0.05), 0px 16px 16px rgba(9, 11, 17, 0.1),
    0px 4px 9px rgba(9, 11, 17, 0.12);

  /* Text Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-md: 1.125rem;
  --text-lg: 1.25rem;
  --text-xl: 1.5rem;
  --text-2xl: 1.875rem;
  --text-3xl: 2.25rem;
  --text-4xl: 3rem;
  --text-5xl: 3.75rem;

  /* Fonts */
  /* Fallback système */
  --font-system: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;

  /* Polices principales */
  --font-brand: "Orbitron", var(--font-system); /* Pour titres, logo */
  --font-body: "Oxanium", var(--font-system); /* Pour textes, UI */
  --font-code: "Space Mono", monospace; /* Pour blocs code */

  /* Transitions */
  --theme-transition: 0.2s ease-in-out;

  /* Layout */
  --header-height: 80px;
}

:root.theme-dark {
  --gray-0: #ffffff;
  --gray-50: #f3f4f7;
  --gray-100: #e3e6ee;
  --gray-200: #c3cadb;
  --gray-300: #a3acc8;
  --gray-400: #8490b5;
  --gray-500: #6474a2;
  --gray-600: #505d84;
  --gray-700: #3d4663;
  --gray-800: #283044;
  --gray-900: #141925;
  --gray-999-basis: 225, 31%, 5%;
  --gray-999: #090b11;

  --accent-light: #1c0056;
  --accent-regular: #7611a6;
  --accent-dark: #c561f6;
  --accent-overlay: hsla(280, 89%, 67%, 0.33);
  --accent-subtle-overlay: hsla(281, 81%, 36%, 0.33);
  --accent-text-over: var(--gray-0);

  --link-color: var(--accent-dark);

  --gradient-stop-1: #4c11c6;
  --gradient-subtle: linear-gradient(
    150deg,
    var(--gray-900) 19%,
    var(--gray-999) 81%
  );
  --gradient-accent-orange: linear-gradient(
    150deg,
    #ca7879,
    var(--accent-regular),
    var(--accent-light)
  );
  --gradient-stroke: linear-gradient(180deg, var(--gray-600), var(--gray-800));

  --shadow-sm: 0px 6px 3px rgba(255, 255, 255, 0.01),
    0px 4px 2px rgba(255, 255, 255, 0.01), 0px 2px 2px rgba(255, 255, 255, 0.02),
    0px 0px 1px rgba(255, 255, 255, 0.03);
  --shadow-md: 0px 28px 11px rgba(255, 255, 255, 0.01),
    0px 16px 10px rgba(255, 255, 255, 0.03),
    0px 7px 7px rgba(255, 255, 255, 0.05), 0px 2px 4px rgba(255, 255, 255, 0.06);
  --shadow-lg: 0px 62px 25px rgba(255, 255, 255, 0.01),
    0px 35px 21px rgba(255, 255, 255, 0.05),
    0px 16px 16px rgba(255, 255, 255, 0.1),
    0px 4px 9px rgba(255, 255, 255, 0.12);
}

html,
body {
  min-height: 100%;
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  background-color: var(--gray-999);
  color: var(--gray-200);
  font-family: var(--font-body);
  -webkit-font-smoothing: antialiased;
  line-height: 1.5;
}

*,
*::after,
*::before {
  box-sizing: border-box;
  margin: 0;
}

/* Prevent horizontal overflow */
* {
  max-width: 100%;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  color: var(--link-color);
}

h1,
h2,
h3,
h4,
h5 {
  line-height: 1.2;
  font-family: var(--font-brand);
  font-weight: 600;
  color: var(--gray-100);
  margin: 0 0 1rem;
}

h1 {
  font-size: var(--text-4xl); /* 3rem / 48px */
}

h2 {
  font-size: var(--text-3xl); /* 2.25rem / 36px */
}

h3 {
  font-size: var(--text-2xl); /* 1.875rem / 30px */
}

h4 {
  font-size: var(--text-xl); /* 1.5rem / 24px */
}

h5 {
  font-size: var(--text-lg); /* 1.25rem / 20px */
}

.subtitle {
  font-weight: 600;
  color: var(--accent-regular);
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.05em;
}

/* Utilities */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.wrapper {
  width: 100%;
  max-width: 83rem;
  margin-inline: auto;
  padding-inline: 1.5rem;
}

.stack {
  display: flex;
  flex-direction: column;
}

.gap-2 {
  gap: 0.5rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-15 {
  gap: 3.75rem;
}
.gap-20 {
  gap: 5rem;
}
.gap-30 {
  gap: 7.5rem;
}
.gap-48 {
  gap: 12rem;
}

.cta-button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: var(--accent-regular);
  color: var(--accent-text-over);
  text-decoration: none;
  font-weight: bold;
  font-size: 1rem; /* <– Ajoute ça si ce n’est pas hérité */
  font-family: "Oxanium", sans-serif; /* ou Orbitron */
  border-radius: 0.5rem;
  transition: background 0.2s ease;
  border: none;
  cursor: pointer;
}

.cta-button:hover {
  filter: brightness(1.1);
}

.cta-link {
  font-family: "Oxanium", sans-serif;
  font-size: 0.85rem;
  color: var(--accent-regular);
  background: var(--accent-text-over);
  padding: 0.4rem 1rem;
  border-radius: 0.5rem;
  margin-top: auto;
  transition: background 0.3s ease, box-shadow 0.3s ease;
}

.cta-link:hover {
  /* background: var(--accent-subtle-overlay); */
  box-shadow: 0 0 10px var(--accent-subtle-overlay);
}

@media (min-width: 50em) {
  .lg\:gap-2 {
    gap: 0.5rem;
  }
  .lg\:gap-4 {
    gap: 1rem;
  }
  .lg\:gap-8 {
    gap: 2rem;
  }
  .lg\:gap-10 {
    gap: 2.5rem;
  }
  .lg\:gap-15 {
    gap: 3.75rem;
  }
  .lg\:gap-20 {
    gap: 5rem;
  }
  .lg\:gap-30 {
    gap: 7.5rem;
  }
  .lg\:gap-48 {
    gap: 12rem;
  }
}

@media (max-width: 600px) {
  :root {
    --text-3xl: 1.75rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
  }
}

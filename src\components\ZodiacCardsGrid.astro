---
// src/components/ZodiacGrid.astro
import { zodiacSigns } from "../data/zodiac";
import ZodiacCard from "./ZodiacCard.astro";
---

<section class="zodiac-grid" id="zodiac-grid">
  <p class="subtitle">Signes du zodiaque</p>

  <h2>Découvre ton horoscope de G@mers</h2>

  <div class="grid">
    {
      zodiacSigns.map((sign) => (
        <ZodiacCard
          name={sign.name}
          slug={sign.slug.toLocaleLowerCase()}
          image={sign.image}
          dateRange={sign.dateRange}
        />
      ))
    }
  </div>
</section>

<style>
  .zodiac-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    background: var(--gray-999);
    border-radius: 3rem 3rem 0 0;
    color: var(--gray-100);
    font-family: "Oxanium", sans-serif;
  }

  .grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 2rem;
  }

  @media (max-width: 1024px) {
    .grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 600px) {
    .zodiac-grid .grid {
      grid-template-columns: 1fr !important;
    }
  }
</style>

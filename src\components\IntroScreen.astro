<script is:inline>
  const alreadySeen = sessionStorage.getItem("hasSeenIntro");

  if (!alreadySeen) {
    document.documentElement.classList.add("with-intro");
    sessionStorage.setItem("hasSeenIntro", "true");

    window.addEventListener("DOMContentLoaded", () => {
      const intro = document.getElementById("intro-screen");
      if (!intro) return;

      setTimeout(() => {
        intro.classList.add("intro-hidden");
        setTimeout(() => {
          intro.remove();
        }, 800);
      }, 1000);
    });
  }
</script>

<div id="intro-screen">
  <div class="intro-content">
    <div class="logo" aria-label="Logo Astrog@mers"></div>
    <p>Chargement...</p>
  </div>
</div>

<style>
  #intro-screen {
    position: fixed;
    inset: 0;
    background: var(--gray-999);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition:
      opacity 0.8s ease,
      visibility 0.8s ease;
  }

  html.with-intro #intro-screen {
    display: flex;
  }

  #intro-screen.intro-hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }

  .logo {
    width: 100px;
    height: 100px;
    margin: 0 auto 1rem;
    background-image: url("/assets/icon.png");
    background-size: contain;
    background-repeat: no-repeat;
    border-radius: 24px;
    animation: pulse 1.5s ease-in-out infinite;
  }

  .intro-content {
    text-align: center;
    animation: fadeInUp 1s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.9;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
</style>

# 🗺️ Roadmap – AstroG@mers (Phase 1)

## ✅ Étape 1 – Dark mode toggle

- [x] Ajouter un bouton pour basculer entre thème clair et thème sombre
- [x] Utiliser la classe `.theme-dark` déjà présente dans `global.css`
- [x] Sauvegarder la préférence de thème dans `localStorage`
- [x] Appliquer automatiquement le thème choisi au rechargement

---

## ✅ Étape 2 – Section Hero (écran d’accueil)

- [x] Créer une section `hero` pleine hauteur (`100vh`)
- [x] Ajouter un titre fort, un sous-titre et un bouton ou icône vers la section suivante
- [x] Ajouter un scroll fluide vers une ancre (``, `#projects`, etc.)
- [ ] Styliser avec les variables CSS ou une nouvelle palette

---

## ✅ Étape 3 – Page About

- [ ] Ajouter une section "À propos de moi" (bio, image, compétences, etc.)
- [x] Ajouter les **mentions légales** (nom, hébergeur, contact, etc.)
- [x] Ajouter une section sur la **confidentialité des données** (RGPD simplifié)

---

## ✅ Étape 4 – Page Contact

- [ ] Ajouter un formulaire de contact (nom, email, message)
- [ ] Ajouter un champ de vérification anti-spam (maths, captcha, etc.)
- [ ] Ajouter un bouton de soumission
- [ ] Ajouter un message de confirmation
- [ ] Ajouter un message d’erreur

---

## ✅ Étape 5 – a propos de nous

- [x] Ajouter une section "À propos de nous" (bio, image, compétences, etc.)
- [x] Le Style et le contenus de ce composant.

---

## ✅ Étape 6 – Horoscope et calcul du signe

- [x] Outil de calcul du signe astrologique
- [x] L'utilisateur entre sa date de naissance
- [x] Le site affiche dynamiquement son signe (sans conservation de données)

---

## ✅ Étape 7 - Grille des 12 signes :

- [x] Présentation sous forme de cartes cliquables
- [x] Chaque carte mène à une page dédiée avec l’horoscope du signe choisi

---

## 📌 Bonus idées (pour plus tard)

- [ ] Page 404 personnalisée
- [x] Intégration des réseaux sociaux (GitHub, LinkedIn...)
- [ ] Menu burger mobile
- [x] Animation au scroll ou transitions douces
- [ ] Partage social

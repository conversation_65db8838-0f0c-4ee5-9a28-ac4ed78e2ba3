---
import Icon from "./Icon.astro";

// Import global styles
import "../styles/fonts.css";
import "../styles/global.css";
---

<script src="/scripts/zodiacFinder.js" type="module"></script>

<section class="zodiac-finder" id="zodiac-finder">
  <div class="zodiac-box">
    <p class="subtitle">Ton signe du zodiaque</p>
    <h2>Découvre ton signe astrologique</h2>

    <div class="form">
      <div class="date-wrapper">
        <input
          type="date"
          id="birthdate"
          lang="fr"
          onkeydown="return false"
          onmousedown="event.preventDefault(); this.showPicker();"
        />
        <span class="icon light" id="calendarTrigger">
          <Icon icon="calendar" />
        </span>
      </div>
      <button id="findZodiac" class="cta-button">Découvrir</button>
    </div>

    <div class="zodiac-result" id="result"></div>
  </div>
</section>

<style>
  .zodiac-finder {
    padding: 4rem 2rem;
    border-radius: 3rem 3rem 0 0;
    color: #fff;
    font-family: "Oxanium", sans-serif;
    text-align: center;
    background-size: cover;
    background-position: center;
  }

  .zodiac-box {
    max-width: 900px;
    margin: 0 auto;
  }

  h2 {
    font-family: "Orbitron", sans-serif;
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  .form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .date-wrapper {
    position: relative;
    display: inline-block;
  }

  .date-wrapper input[type="date"] {
    padding-right: 2.5rem;
  }

  .date-wrapper .icon {
    position: absolute;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    pointer-events: none;
    font-size: 1.1rem;
    opacity: 0.8;
    color: var(--gray-100);
  }

  input[type="date"] {
    appearance: none;
    background-color: var(--gray-700);
    border: 1px solid var(--gray-600);
    border-radius: 12px;
    padding: 0.75rem 1.25rem;
    color: var(--gray-100);
    font-family: "Oxanium", sans-serif;
    font-size: 1rem;
    cursor: pointer;
  }

  input[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
  }

  input[type="date"]:hover {
    border-color: var(--accent-regular);
  }

  .cta-button {
    font-size: 1rem;
    padding: 0.8rem 1.8rem;
    border-radius: 0.75rem;
    background: var(--accent-regular);
    color: white;
    font-family: "Oxanium", sans-serif;
    box-shadow: 0 0 12px rgba(198, 97, 246, 0.25);
    transition: all 0.3s ease;
  }

  .cta-button:hover {
    background: var(--accent-light);
    box-shadow: 0 0 20px rgba(198, 97, 246, 0.5);
  }

  /* Zodiac Result */
  .zodiac-result {
    margin-top: 2.5rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    animation: fadeIn 0.4s ease-in-out;
  }

  .zodiac-result-card {
    background: linear-gradient(145deg, #1a1a2e, #12121c);
    border: 1px solid rgba(198, 97, 246, 0.2);
    border-radius: 1.5rem;
    padding: 2rem;
    max-width: 400px;
    margin: 2rem auto 0;
    box-shadow: 0 0 20px rgba(198, 97, 246, 0.08);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.4s ease;
  }

  .zodiac-icon-result {
    width: 96px;
    height: 96px;
    object-fit: contain;
    margin-bottom: 1rem;
    filter: drop-shadow(0 0 8px rgba(198, 97, 246, 0.3));
  }

  .zodiac-result-heading {
    font-size: 0.9rem;
    color: var(--gray-500);
    text-transform: uppercase;
    margin-bottom: 0.5rem;
  }

  .zodiac-result-name {
    font-family: "Orbitron", sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-regular);
    margin-bottom: 1.5rem;
    text-shadow: 0 0 10px rgba(198, 97, 246, 0.3);
  }

  /* Animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes cosmicPop {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .fade-in {
    animation: cosmicPop 0.5s ease-out both;
  }
</style>

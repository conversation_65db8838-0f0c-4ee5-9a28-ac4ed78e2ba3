---
// Page section component
import <PERSON> from "../components/Hero.astro";
import AboutPreview from "../components/AboutPreview.astro";
import ZodiacCardsGrid from "../components/ZodiacCardsGrid.astro";
import ZodiacFinder from "../components/ZodiacFinder.astro";
import ScrollToTop from "../components/ScrollToTop.astro";

// Layout import — provides basic page elements: <head>, <nav>, <footer> etc.
import BaseLayout from "../layouts/BaseLayout.astro";
---

<script is:inline>
  if (sessionStorage.getItem("scrollToZodiac")) {
    sessionStorage.removeItem("scrollToZodiac");

    // scroll dès que possible sans attendre paint complet
    window.addEventListener("DOMContentLoaded", () => {
      const target = document.querySelector("#zodiac-grid");
      if (target) {
        target.scrollIntoView({ behavior: "instant", block: "start" });
      }
    });
  }
</script>

<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>Astrog@mers</title>
  </head>

  <body>
    <BaseLayout>
      <Hero />
      <AboutPreview />
      <ZodiacCardsGrid />
      <ZodiacFinder />
      <ScrollToTop />
    </BaseLayout>
  </body>
</html>

---
import Icon from "./Icon.astro";

const { signe, eloCosmique, prediction, compatibility, recommendation } =
  Astro.props;

const today = new Date().toLocaleDateString("fr-FR", {
  weekday: "long",
  year: "numeric",
  month: "long",
  day: "numeric",
});
---

<section class="zodiac-page">
  <div class="back-button-wrapper">
    <a
      href="/"
      class="back-button"
      onclick="sessionStorage.setItem('scrollToZodiac', '1')"
    >
      <Icon icon="arrow-left" />
      Retour à la grille
    </a>
  </div>

  <h1>{signe}</h1>
  <p class="date">{today}</p>

  <div class="horoscope-block">
    <h2>Élo cosmique</h2>
    <p>{eloCosmique}</p>
  </div>

  <div class="horoscope-block">
    <h2>Prédiction du jour</h2>
    <p>{prediction}</p>
  </div>

  <div class="horoscope-block">
    <h2>Compatibilité galactique</h2>
    <p>{compatibility}</p>
  </div>

  <div class="horoscope-block">
    <h2>Conseil du jour</h2>
    <p>{recommendation}</p>
  </div>
</section>

<style>
  .zodiac-page {
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
    font-family: "Oxanium", sans-serif;
    color: white;
    animation: fadeIn 0.8s ease;
    position: relative;
  }

  .back-button {
    display: inline-block;
    margin-bottom: 1rem;
    font-family: "Oxanium", sans-serif;
    font-size: 0.9rem;
    color: #fff;
    text-decoration: none;
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    transition:
      background 0.2s ease,
      box-shadow 0.2s ease;
  }

  .back-button:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  h1 {
    text-align: center;
  }

  .date {
    text-align: center;
    color: #aaa;
    font-size: 0.9rem;
    margin-bottom: 2rem;
  }

  .horoscope-block {
    background: var(--gray-700);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(128, 0, 255, 0.05);
  }

  p {
    font-size: 1rem;
    line-height: 1.6;
  }
</style>

---
import { Image } from "astro:assets";
import HeroImage from "../assets/hero.webp";
import AstroWheel from "../assets/astro-wheel.webp";
import Icon from "./Icon.astro";
---

<section class="hero">
  <div class="hero-galaxy-bg"></div>
  <div class="hero-content">
    <div class="hero-text">
      <p class="subtitle">Bienvenue sur Astrog@mers</p>
      <h1 class="title">Les étoiles en savent plus sur toi que tes mates</h1>
      <p class="description">
        <PERSON>que jour, les étoiles mettent à jour ton Elo cosmique. Style de jeu,
        humeur, compatibilités : tout est dans les astres.
      </p>
      <a href="#zodiac-grid" class="cta-button">Découvrir</a>
    </div>

    <div class="hero-image">
      <Image
        src={HeroImage}
        alt="Hero Astrog@mers"
        width={600}
        height={400}
        format="webp"
        class="hero-img"
        loading="eager"
        decoding="async"
      />
      <div class="astro-wheel">
        <Image
          src={AstroWheel}
          alt="AstroWheel"
          width={256}
          height={256}
          format="webp"
          class="wheel-img"
          loading="eager"
          decoding="async"
        />
      </div>
      <div class="hero-badges">
        <div class="stat-block">
          <div class="stat-number">95%</div>
          <div class="stat-label">fiabilité</div>
        </div>
        <hr />
        <div class="stat-block">
          <div class="stat-number">980+</div>
          <div class="stat-label">lectures</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Chevron pour le scroll -->
  <a
    href="#about-preview"
    class="hero-chevron"
    aria-label="Scroll vers la suite"
  >
    <span class="icon light"><Icon icon="arrow-down" /></span></a
  >
</section>

<style>
  .hero {
    position: relative;
    min-height: calc(100vh - var(--header-height, 80px));
    background: linear-gradient(135deg, #1a0d30 0%, #0a0614 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .hero-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    gap: 2rem;
  }

  .hero-text {
    flex: 1 1 500px;
  }

  .hero-text .title {
    font-size: 3rem;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: #e3e6ee;
  }

  .description {
    margin-bottom: 1.5rem;
    color: #a3acc8;
  }

  .stats {
    display: flex;
    gap: 2rem;
    color: var(--gray-200);
    font-weight: 500;
  }

  .hero-image {
    flex: 1 1 400px;
    text-align: center;
    position: relative; /* ← nécessaire */
  }

  .hero-text {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0; /* important */
    animation-delay: 0.3s;
  }

  .hero-img {
    max-width: 100%;
    height: auto;
    border-radius: 1rem;
    object-fit: cover;
  }

  .hero-chevron {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2rem;
    color: var(--accent-regular);
    animation: bounce 2s infinite;
    text-decoration: none;
  }

  .hero-badges {
    position: absolute;
    top: 80%;
    right: -20px;
    transform: translateY(-50%);
    width: 120px;
    height: 260px;
    background: linear-gradient(
      180deg,
      var(--accent-light) 0%,
      var(--accent-regular) 60%,
      var(--accent-dark) 100%
    );
    border-radius: 999px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-family: "Oxanium", sans-serif;
    padding: 1.5rem 0.5rem;
    color: white;
    z-index: 2;
    gap: 1.2rem;
  }

  .hero-badges::before {
    content: "";
    position: absolute;
    inset: 0;
    background: radial-gradient(
      circle at 50% 20%,
      var(--accent-overlay),
      transparent 60%
    );
    border-radius: inherit;
    z-index: -1;
  }

  .stat-block .stat-number {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
  }

  .stat-block .stat-label {
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.85;
  }

  .hero-badges hr {
    border: none;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    width: 60%;
    margin: 0.5rem 0;
  }

  .astro-wheel {
    position: absolute;
    top: 40px; /* ou ajuste selon ta roue */
    right: -130px;
    width: 256px;
    height: 256px;
    z-index: 1;
  }

  .wheel-img {
    width: 100%;
    height: auto;
    opacity: 0.6; /* si tu veux un effet plus subtil */
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.15));
  }

  @keyframes bounce {
    0%,
    100% {
      transform: translateX(-50%) translateY(0);
    }
    50% {
      transform: translateX(-50%) translateY(10px);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @media (max-width: 768px) {
    .hero-content {
      flex-direction: column-reverse;
      text-align: center;
    }

    .hero-text .title {
      font-size: 2.2rem;
    }
  }

  @media (max-width: 1024px) {
    .hero-image {
      display: none;
    }
  }

  @media (max-width: 1400px) {
    .astro-wheel {
      display: none;
    }
  }
</style>

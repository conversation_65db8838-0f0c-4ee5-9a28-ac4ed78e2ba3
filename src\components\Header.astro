---
import { Image } from "astro:assets";
import Logo from "../assets/logo.svg";

import ThemeToggle from "./ThemeToggle.astro";
---

<header class="site-header">
  <div class="wrapper nav-wrapper">
    <nav class="site-nav">
      <ul>
        <li><a href="/">Accueil</a></li>
        <li><a href="/about">À propos</a></li>
      </ul>
    </nav>

    <div class="logo-title">
      <Image
        src={Logo}
        alt="Logo Astrog@mers"
        class="logo"
        loading="eager"
        decoding="async"
      />
    </div>

    <div class="left-nav">
      <ThemeToggle />
    </div>
  </div>
</header>

<style>
  .site-header {
    background-color: var(--gray-900);
    box-shadow: var(--shadow-sm);
    /* éviter un header figé si le contenu wrap */
    min-height: var(--header-height);
  }

  .nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    /* pas de wrap en desktop */
    flex-wrap: nowrap;
  }

  .logo-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    flex-shrink: 0;
  }

  /* ❗️Le problème : width:100% sur le logo → on le retire */
  .logo {
    /* le logo respecte la hauteur du header sans déborder */
    height: calc(var(--header-height) - 16px);
    width: auto;
    padding: 0.5rem 1rem;
    color: var(--accent-regular);
    max-width: 220px; /* sécurité */
  }

  .site-nav ul {
    list-style: none;
    display: flex;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
  }

  .site-nav a {
    text-decoration: none;
    color: var(--gray-200);
    font-weight: 500;
    transition: color var(--theme-transition);
  }

  .nav-wrapper.wrapper {
    max-width: 83rem;
    margin-inline: auto;
    padding: 12px 1.5rem;
    height: 100%;
  }

  .left-nav {
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  /* ---------- Mobile ---------- */
  @media (max-width: 768px) {
    .nav-wrapper {
      flex-wrap: wrap;
      row-gap: 0.25rem;
      gap: 0.75rem;
    }

    .site-nav {
      order: 1;
      width: 100%;
    }
    .logo-title {
      order: 2;
      flex: 1;
      justify-content: center;
    }
    .left-nav {
      order: 3;
      margin-left: auto;
    }

    .site-nav ul {
      justify-content: center;
      gap: 0.75rem;
      flex-wrap: wrap;
    }

    .site-nav a {
      font-size: 0.9rem;
    }

    .logo {
      height: 40px;
      width: auto;
      padding: 0.25rem 0.5rem;
    }

    .site-header {
      min-height: auto;
    }
  }
</style>

name: Génération quotidienne des horoscopes

on:
  schedule:
    # S'exécute tous les jours à 00:01 UTC
    - cron: "1 0 * * *"
  workflow_dispatch:

jobs:
  generate-horoscope:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - name: Checkout du repo
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Installation des dépendances (y compris ts-node)
        run: npm ci

      - name: Exécution du script de génération
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
        run: node --loader ts-node/esm scripts/generate-horoscope.ts

      - name: Commit et push des horoscopes
        run: |
          git config --global user.name "AstroBot 🤖"
          git config --global user.email "<EMAIL>"
          git add src/data/horoscopes.ts
          # Vérifie s'il y a des changements à commit avant de le faire
          git diff-index --quiet HEAD || git commit -m "♻️ Horoscope du jour auto-généré"
          git push
